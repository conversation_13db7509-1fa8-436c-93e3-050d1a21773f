@echo off
title AutoClick GUI - Iniciador Inteligente
color 0A

echo.
echo ================================================
echo   🖱️ AUTOCLICK GUI - INICIADOR INTELIGENTE
echo ================================================
echo.

REM Verificar si Python está disponible
echo [1/3] Verificando Python...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python encontrado
    goto :python_found
) else (
    echo ✗ Python NO encontrado
    goto :no_python
)

:python_found
echo.
echo [2/3] Verificando archivos del programa...

REM Verificar archivos principales
if not exist "autoclick.py" (
    echo ✗ autoclick.py NO encontrado
    goto :missing_files
)

if not exist "get_coordinates.py" (
    echo ✗ get_coordinates.py NO encontrado
    goto :missing_files
)

echo ✓ Archivos del programa encontrados
echo.
echo [3/3] Mostrando opciones disponibles...
echo.

:show_menu
echo ================================================
echo   🎮 MENU DE OPCIONES
echo ================================================
echo.
echo 1. 🚀 Ejecutar AutoClick GUI (Programa principal)
echo 2. 🖱️ Capturar Coordenadas (Herramienta de configuración)
echo 3. 🧪 Ejecutar Pruebas del Sistema
echo 4. 🔧 Verificar Sistema Completo
echo 5. 📖 Ver Instrucciones de Instalación
echo 6. 🏗️ Compilar a Ejecutable
echo 7. ❌ Salir
echo.
set /p choice="🎯 Selecciona una opción (1-7): "

if "%choice%"=="1" goto :run_main
if "%choice%"=="2" goto :run_coordinates
if "%choice%"=="3" goto :run_tests
if "%choice%"=="4" goto :run_verification
if "%choice%"=="5" goto :show_instructions
if "%choice%"=="6" goto :compile
if "%choice%"=="7" goto :exit

echo ❌ Opción inválida. Por favor selecciona 1-7.
echo.
goto :show_menu

:run_main
echo.
echo 🚀 Iniciando AutoClick GUI...
echo ================================================
python autoclick.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ Error ejecutando AutoClick GUI
    echo 💡 Verifica que todas las dependencias estén instaladas
)
goto :end

:run_coordinates
echo.
echo 🖱️ Iniciando Capturador de Coordenadas...
echo ================================================
python get_coordinates.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ Error ejecutando Capturador de Coordenadas
)
goto :end

:run_tests
echo.
echo 🧪 Ejecutando Pruebas del Sistema...
echo ================================================
python test_coordinates.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ Error ejecutando pruebas
)
goto :end

:run_verification
echo.
echo 🔧 Verificando Sistema Completo...
echo ================================================
call verificar_sistema.bat
goto :end

:show_instructions
echo.
echo 📖 Abriendo instrucciones de instalación...
if exist "INSTRUCCIONES_INSTALACION.md" (
    start notepad "INSTRUCCIONES_INSTALACION.md"
) else (
    echo ❌ Archivo de instrucciones no encontrado
)
goto :end

:compile
echo.
echo 🏗️ Compilando a ejecutable...
echo ================================================
call build.bat
goto :end

:no_python
echo.
echo ================================================
echo   ❌ PYTHON NO ENCONTRADO
echo ================================================
echo.
echo Para usar AutoClick GUI necesitas instalar Python:
echo.
echo 1. Ve a: https://python.org/downloads/
echo 2. Descarga Python 3.8 o superior
echo 3. Durante la instalación, marca "Add Python to PATH"
echo 4. Reinicia esta terminal y ejecuta este script nuevamente
echo.
echo Alternativamente, puedes:
echo - Usar el ejecutable precompilado (si está disponible)
echo - Instalar Python desde Microsoft Store
echo.
goto :end

:missing_files
echo.
echo ================================================
echo   ❌ ARCHIVOS FALTANTES
echo ================================================
echo.
echo No se encontraron todos los archivos necesarios.
echo Asegúrate de que estén en el mismo directorio:
echo.
echo - autoclick.py (programa principal)
echo - get_coordinates.py (capturador de coordenadas)
echo - config.json (se crea automáticamente)
echo.
goto :end

:end
echo.
echo ================================================
echo   ⏳ PROGRAMA FINALIZADO
echo ================================================
echo.
echo ¿Quieres ejecutar otra opción?
set /p again="(S/N): "
if /i "%again%"=="S" goto :show_menu
if /i "%again%"=="SI" goto :show_menu
if /i "%again%"=="SÍ" goto :show_menu
if /i "%again%"=="Y" goto :show_menu
if /i "%again%"=="YES" goto :show_menu

:exit
echo.
echo 👋 ¡Gracias por usar AutoClick GUI!
echo.
pause
exit /b 0
