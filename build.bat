@echo off
echo ================================================
echo   AutoClick GUI - Compilador Ultraligero
echo ================================================

echo.
echo [1/3] Verificando Python...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python no encontrado. Instala Python 3.7+ desde python.org
    pause
    exit /b 1
)

echo.
echo [2/3] Instalando PyInstaller (única dependencia)...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo ERROR: No se pudo instalar PyInstaller
    pause
    exit /b 1
)

echo.
echo [3/3] Compilando ejecutable con interfaz gráfica...
pyinstaller --onefile --windowed --name "AutoClickGUI" --exclude-module matplotlib --exclude-module numpy --exclude-module PIL autoclick.py

if %errorlevel% neq 0 (
    echo ERROR: Fallo en la compilación
    pause
    exit /b 1
)

echo.
echo ================================================
echo   COMPILACION EXITOSA!
echo ================================================
echo.
echo Ejecutable: dist\AutoClickGUI.exe
echo Tamaño: ~3-5 MB (ultraligero!)
echo.
echo ARCHIVOS NECESARIOS:
echo 1. AutoClickGUI.exe (ejecutable principal)
echo 2. config.json (se crea automáticamente)
echo.
echo El programa creará automáticamente:
echo - config.json (configuración)
echo - autoclicker_log.txt (registro de actividad)
echo.
echo CARACTERÍSTICAS:
echo ✓ Interfaz gráfica intuitiva
echo ✓ Captura de coordenadas automática
echo ✓ Control de inicio/parada
echo ✓ Logs en tiempo real
echo ✓ Configuración visual
echo ✓ Modo de prueba
echo.
pause
