#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoClick Lite - Versión ultraligera usando solo bibliotecas estándar
Peso: <2MB cuando se compila
Dependencias: Solo bibliotecas estándar de Python + ctypes (incluido)
"""

import time
import random
import configparser
import logging
import os
import sys
from datetime import datetime
import ctypes
from ctypes import wintypes
import json

# Constantes de Windows API
VK_F5 = 0x74
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
SW_RESTORE = 9

class AutoClickerLite:
    def __init__(self):
        self.setup_logging()
        self.load_config()
        self.running = True
        
        # Cargar bibliotecas de Windows
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32
        
    def setup_logging(self):
        """Configurar sistema de logging simple"""
        logging.basicConfig(
            filename='autoclicker_log.txt',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.logger = logging.getLogger(__name__)
        
        # También log a consola
        console = logging.StreamHandler()
        console.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(message)s')
        console.setFormatter(formatter)
        self.logger.addHandler(console)
        
    def load_config(self):
        """Cargar configuración desde archivo JSON (más ligero que ConfigParser)"""
        config_file = 'config.json'
        
        if not os.path.exists(config_file):
            self.create_default_config()
            
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            self.click_coords = config['coordinates']
            self.reload_delay = config['timing']['reload_delay_seconds']
            self.cycle_hours = config['timing']['cycle_hours']
            self.min_delay = config['timing']['min_click_delay_ms']
            self.max_delay = config['timing']['max_click_delay_ms']
            self.browser_keywords = config['browser']['window_keywords']
            
        except Exception as e:
            self.logger.error(f"Error cargando configuración: {e}")
            self.create_default_config()
            self.load_config()
            
    def create_default_config(self):
        """Crear archivo de configuración por defecto en JSON"""
        default_config = {
            "coordinates": [
                [100, 200], [200, 300], [300, 400], [400, 500],
                [500, 600], [600, 700], [700, 800]
            ],
            "timing": {
                "reload_delay_seconds": 20,
                "cycle_hours": 2,
                "min_click_delay_ms": 50,
                "max_click_delay_ms": 150
            },
            "browser": {
                "window_keywords": ["chrome", "firefox", "edge", "opera", "brave"]
            }
        }
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
            
        self.logger.info("Archivo config.json creado con valores por defecto")
        
    def click_at(self, x, y):
        """Realizar click usando Windows API directamente"""
        try:
            # Mover cursor
            self.user32.SetCursorPos(x, y)
            time.sleep(0.01)
            
            # Click down
            self.user32.mouse_event(MOUSEEVENTF_LEFTDOWN, x, y, 0, 0)
            time.sleep(0.01)
            
            # Click up
            self.user32.mouse_event(MOUSEEVENTF_LEFTUP, x, y, 0, 0)
            
            return True
        except Exception as e:
            self.logger.error(f"Error en click ({x}, {y}): {e}")
            return False
            
    def press_f5(self):
        """Presionar F5 usando Windows API"""
        try:
            # Simular presión de tecla F5
            self.user32.keybd_event(VK_F5, 0, 0, 0)  # Key down
            time.sleep(0.05)
            self.user32.keybd_event(VK_F5, 0, 2, 0)  # Key up
            return True
        except Exception as e:
            self.logger.error(f"Error presionando F5: {e}")
            return False
            
    def find_browser_window(self):
        """Encontrar ventana del navegador usando Windows API"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value.lower()
                    
                    for keyword in self.browser_keywords:
                        if keyword.lower() in window_title:
                            # Activar ventana
                            self.user32.ShowWindow(hwnd, SW_RESTORE)
                            self.user32.SetForegroundWindow(hwnd)
                            self.logger.info(f"Ventana activada: {buffer.value}")
                            return False  # Detener enumeración
            return True
            
        try:
            # Definir el tipo de función callback
            EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, 
                                               ctypes.POINTER(ctypes.c_int), 
                                               ctypes.POINTER(ctypes.c_int))
            
            # Enumerar ventanas
            self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
            time.sleep(0.5)  # Esperar activación
            return True
            
        except Exception as e:
            self.logger.warning(f"Error buscando ventana del navegador: {e}")
            return False
            
    def perform_clicks(self):
        """Realizar los 7 clicks con delays aleatorios"""
        self.logger.info("Iniciando secuencia de clicks")
        
        for i, (x, y) in enumerate(self.click_coords, 1):
            # Delay aleatorio entre clicks
            delay = random.randint(self.min_delay, self.max_delay) / 1000.0
            time.sleep(delay)
            
            # Realizar click
            if self.click_at(x, y):
                self.logger.info(f"Click {i}/7 en ({x}, {y}) - Delay: {delay:.3f}s")
            else:
                self.logger.error(f"Fallo click {i}/7 en ({x}, {y})")
                
    def reload_page(self):
        """Recargar página con F5"""
        if self.press_f5():
            self.logger.info("Página recargada (F5)")
        else:
            self.logger.error("Error recargando página")
            
    def run_cycle(self):
        """Ejecutar un ciclo completo"""
        cycle_start = datetime.now()
        self.logger.info(f"=== INICIANDO CICLO - {cycle_start.strftime('%H:%M:%S')} ===")
        
        # Buscar y activar navegador
        if not self.find_browser_window():
            self.logger.warning("Continuando sin activar ventana específica del navegador")
            
        # Realizar clicks
        self.perform_clicks()
        
        # Esperar y recargar
        self.logger.info(f"Esperando {self.reload_delay} segundos...")
        time.sleep(self.reload_delay)
        self.reload_page()
        
        cycle_end = datetime.now()
        duration = (cycle_end - cycle_start).total_seconds()
        self.logger.info(f"=== CICLO COMPLETADO - {duration:.1f}s ===")
        
        return True
        
    def run(self):
        """Bucle principal"""
        self.logger.info("AutoClicker Lite iniciado")
        self.logger.info(f"Configuración: {len(self.click_coords)} clicks cada {self.cycle_hours}h")
        
        try:
            while self.running:
                self.run_cycle()
                
                # Calcular tiempo de espera
                wait_seconds = self.cycle_hours * 3600
                self.logger.info(f"Esperando {self.cycle_hours} horas hasta el próximo ciclo...")
                
                # Esperar con verificaciones cada minuto
                for i in range(wait_seconds):
                    if not self.running:
                        break
                    if i % 60 == 0:  # Log cada minuto
                        remaining_hours = (wait_seconds - i) / 3600
                        if remaining_hours > 0.1:  # Solo si quedan más de 6 minutos
                            self.logger.info(f"Tiempo restante: {remaining_hours:.1f}h")
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            self.logger.info("Programa detenido por el usuario")
        except Exception as e:
            self.logger.error(f"Error crítico: {e}")
        finally:
            self.logger.info("AutoClicker Lite finalizado")

def main():
    """Función principal"""
    print("=" * 55)
    print("  AutoClick Lite - Versión Ultraligera (<2MB)")
    print("=" * 55)
    print("✓ Sin dependencias externas")
    print("✓ Portable y autónomo") 
    print("✓ Configuración en config.json")
    print("✓ Logs en autoclicker_log.txt")
    print("-" * 55)
    print("Presiona Ctrl+C para detener")
    print("=" * 55)
    
    try:
        clicker = AutoClickerLite()
        clicker.run()
    except Exception as e:
        print(f"Error fatal: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
