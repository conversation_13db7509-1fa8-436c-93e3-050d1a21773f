#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Script de Prueba para el Capturador de Coordenadas
Versión simple para verificar que todo funciona correctamente
"""

import ctypes
import time
import json
import os

def test_mouse_position():
    """Probar obtención de posición del mouse"""
    print("🧪 Probando obtención de posición del mouse...")
    
    try:
        user32 = ctypes.windll.user32
        point = ctypes.wintypes.POINT()
        
        print("📍 Mueve el mouse y observa las coordenadas (10 segundos):")
        
        for i in range(50):  # 10 segundos, 5 lecturas por segundo
            user32.GetCursorPos(ctypes.byref(point))
            print(f"\r🖱️  Posición actual: ({point.x:4d}, {point.y:4d})", end="", flush=True)
            time.sleep(0.2)
            
        print(f"\n✅ Prueba de posición del mouse completada")
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de mouse: {e}")
        return False

def test_json_operations():
    """Probar operaciones con JSON"""
    print("\n🧪 Probando operaciones con archivos JSON...")
    
    try:
        # Crear datos de prueba
        test_data = {
            "coordinates": [[100, 200], [300, 400]],
            "timing": {"reload_delay_seconds": 20},
            "test": True
        }
        
        # Escribir archivo
        test_file = "test_config.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        print(f"✅ Archivo JSON creado: {test_file}")
        
        # Leer archivo
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        print(f"✅ Archivo JSON leído correctamente")
        
        # Verificar datos
        if loaded_data == test_data:
            print(f"✅ Datos JSON verificados correctamente")
        else:
            print(f"⚠️  Advertencia: Los datos no coinciden")
            
        # Limpiar archivo de prueba
        os.remove(test_file)
        print(f"🗑️  Archivo de prueba eliminado")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba JSON: {e}")
        return False

def test_keyboard_detection():
    """Probar detección de teclado"""
    print("\n🧪 Probando detección de teclado...")
    
    try:
        import msvcrt
        print("✅ msvcrt disponible - Modo interactivo habilitado")
        print("⌨️  Presiona cualquier tecla (ESC para salir):")
        
        while True:
            if msvcrt.kbhit():
                key = msvcrt.getch()
                if key == b'\x1b':  # ESC
                    print("\n✅ Prueba de teclado completada")
                    break
                else:
                    print(f"\n🔘 Tecla presionada: {key}")
                    print("⌨️  Presiona otra tecla (ESC para salir):")
                    
            time.sleep(0.1)
            
        return True
        
    except ImportError:
        print("⚠️  msvcrt no disponible - Solo modo automático")
        return True
    except Exception as e:
        print(f"❌ Error en prueba de teclado: {e}")
        return False

def test_cursor_movement():
    """Probar movimiento del cursor"""
    print("\n🧪 Probando movimiento del cursor...")
    
    try:
        user32 = ctypes.windll.user32
        
        # Obtener posición inicial
        point = ctypes.wintypes.POINT()
        user32.GetCursorPos(ctypes.byref(point))
        initial_x, initial_y = point.x, point.y
        print(f"📍 Posición inicial: ({initial_x}, {initial_y})")
        
        # Mover cursor en un patrón
        test_positions = [
            (initial_x + 100, initial_y),
            (initial_x + 100, initial_y + 100),
            (initial_x, initial_y + 100),
            (initial_x, initial_y)  # Volver al inicio
        ]
        
        print("🔄 Moviendo cursor en patrón cuadrado...")
        for i, (x, y) in enumerate(test_positions, 1):
            user32.SetCursorPos(x, y)
            print(f"   {i}. Movido a ({x}, {y})")
            time.sleep(1)
            
        print("✅ Prueba de movimiento del cursor completada")
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de cursor: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🧪" * 40)
    print("  AUTOCLICK GUI - SUITE DE PRUEBAS")
    print("🧪" * 40)
    print()
    
    tests = [
        ("Posición del Mouse", test_mouse_position),
        ("Operaciones JSON", test_json_operations),
        ("Detección de Teclado", test_keyboard_detection),
        ("Movimiento del Cursor", test_cursor_movement)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 EJECUTANDO: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: EXITOSO")
            else:
                print(f"❌ {test_name}: FALLIDO")
                
        except KeyboardInterrupt:
            print(f"\n⏹️  {test_name}: CANCELADO POR USUARIO")
            results.append((test_name, False))
            break
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Mostrar resumen
    print(f"\n{'🎯'*50}")
    print("  RESUMEN DE PRUEBAS")
    print(f"{'🎯'*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ EXITOSO" if result else "❌ FALLIDO"
        print(f"{status:12} - {test_name}")
    
    print(f"\n📊 RESULTADO FINAL: {passed}/{total} pruebas exitosas")
    
    if passed == total:
        print("🎉 ¡Todas las pruebas pasaron! El sistema está listo.")
        print("🚀 Puedes ejecutar: python get_coordinates.py")
    else:
        print("⚠️  Algunas pruebas fallaron. Revisa los errores anteriores.")
        
    print(f"\n{'🧪'*50}")
    
    input("\n⏳ Presiona Enter para salir...")

if __name__ == "__main__":
    main()
