#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🖱️ Capturador de Coordenadas Mejorado para AutoClick GUI
Versión 2.0 - Ultraligero y fácil de usar
"""

import time
import ctypes
import json
import os
import sys
from datetime import datetime

class CoordinateHelper:
    def __init__(self):
        try:
            self.user32 = ctypes.windll.user32
            self.coordinates = []
            self.max_coords = 10  # Permitir hasta 10 coordenadas
            self.test_mode = False
        except Exception as e:
            print(f"❌ Error inicializando sistema: {e}")
            print("💡 Asegúrate de estar ejecutando en Windows")
            sys.exit(1)

    def get_mouse_position(self):
        """Obtener posición actual del mouse con manejo de errores"""
        try:
            point = ctypes.wintypes.POINT()
            self.user32.GetCursorPos(ctypes.byref(point))
            return point.x, point.y
        except Exception as e:
            print(f"❌ Error obteniendo posición del mouse: {e}")
            return 0, 0

    def show_welcome(self):
        """Mostrar pantalla de bienvenida mejorada"""
        print("\n" + "🖱️" * 30)
        print("  CAPTURADOR DE COORDENADAS AUTOCLICK GUI v2.0")
        print("🖱️" * 30)
        print()
        print("🎯 CARACTERÍSTICAS:")
        print("   ✅ Captura hasta 10 coordenadas")
        print("   ✅ Visualización en tiempo real")
        print("   ✅ Modo de prueba integrado")
        print("   ✅ Guardado automático")
        print("   ✅ Manejo robusto de errores")
        print()
        print("📋 INSTRUCCIONES:")
        print("   1️⃣  Abre tu navegador en la página objetivo")
        print("   2️⃣  Posiciona el mouse sobre cada punto de click")
        print("   3️⃣  Presiona ESPACIO para capturar coordenada")
        print("   4️⃣  Presiona ENTER cuando termines")
        print()
        print("⌨️  CONTROLES:")
        print("   🔘 ESPACIO  → Capturar coordenada actual")
        print("   🔘 ENTER   → Finalizar captura")
        print("   🔘 ESC     → Cancelar y salir")
        print("   🔘 T       → Modo de prueba (test)")
        print("   🔘 C       → Limpiar coordenadas")
        print()
        print("🖱️" * 30)
        print(f"📍 Coordenadas capturadas: 0/{self.max_coords}")
        print("🖱️" * 30)
        
    def capture_coordinates(self):
        """Capturar coordenadas con interfaz mejorada"""
        self.show_welcome()

        # Detectar método de captura disponible
        try:
            import msvcrt
            return self.capture_interactive_mode()
        except ImportError:
            print("⚠️  msvcrt no disponible. Usando modo automático...")
            return self.capture_auto_mode()

    def capture_interactive_mode(self):
        """Modo interactivo con detección de teclas"""
        import msvcrt

        print("\n🚀 MODO INTERACTIVO ACTIVADO")
        print("💡 Mueve el mouse y presiona las teclas según necesites\n")

        last_pos = (0, 0)

        while len(self.coordinates) < self.max_coords:
            # Obtener posición actual
            x, y = self.get_mouse_position()

            # Solo actualizar si la posición cambió (reduce parpadeo)
            if (x, y) != last_pos:
                self.update_status_line(x, y)
                last_pos = (x, y)

            # Verificar teclas presionadas
            if msvcrt.kbhit():
                key = msvcrt.getch()

                if key == b' ':  # ESPACIO - Capturar
                    self.add_coordinate(x, y)

                elif key == b'\r':  # ENTER - Finalizar
                    if len(self.coordinates) > 0:
                        print(f"\n\n🎉 Captura finalizada con {len(self.coordinates)} coordenadas!")
                        return True
                    else:
                        print("\n⚠️  Debes capturar al menos 1 coordenada antes de finalizar")

                elif key == b'\x1b':  # ESC - Cancelar
                    print("\n\n❌ Captura cancelada por el usuario")
                    return False

                elif key.lower() == b't':  # T - Modo de prueba
                    self.toggle_test_mode()

                elif key.lower() == b'c':  # C - Limpiar
                    self.clear_coordinates()

                elif key.lower() == b'h':  # H - Ayuda
                    self.show_help()

            time.sleep(0.05)  # Reducir CPU usage

        print(f"\n\n🎯 Máximo de coordenadas alcanzado ({self.max_coords})")
        return True

    def update_status_line(self, x, y):
        """Actualizar línea de estado"""
        status = f"\r📍 Posición: ({x:4d}, {y:4d}) | "
        status += f"Capturadas: {len(self.coordinates)}/{self.max_coords} | "
        status += f"{'🧪 MODO PRUEBA' if self.test_mode else '🎯 MODO CAPTURA'} | "
        status += "Presiona H para ayuda"
        print(status, end="", flush=True)

    def add_coordinate(self, x, y):
        """Agregar coordenada con validación"""
        if self.test_mode:
            print(f"\n🧪 PRUEBA: Click simulado en ({x}, {y})")
            self.simulate_click(x, y)
        else:
            # Verificar si la coordenada ya existe
            if [x, y] in self.coordinates:
                print(f"\n⚠️  Coordenada ({x}, {y}) ya existe. Ignorando...")
                return

            self.coordinates.append([x, y])
            print(f"\n✅ Coordenada {len(self.coordinates)} capturada: ({x}, {y})")

            # Mostrar progreso
            if len(self.coordinates) < self.max_coords:
                remaining = self.max_coords - len(self.coordinates)
                print(f"   📊 Progreso: {len(self.coordinates)}/{self.max_coords} ({remaining} restantes)")

    def simulate_click(self, x, y):
        """Simular click para modo de prueba"""
        try:
            # Mover cursor a la posición
            self.user32.SetCursorPos(x, y)
            time.sleep(0.1)

            # Simular click (sin hacer click real)
            print(f"   🖱️  Cursor movido a ({x}, {y})")
            print("   ✨ Click simulado (modo prueba)")
        except Exception as e:
            print(f"   ❌ Error en simulación: {e}")

    def toggle_test_mode(self):
        """Alternar modo de prueba"""
        self.test_mode = not self.test_mode
        mode = "🧪 MODO PRUEBA" if self.test_mode else "🎯 MODO CAPTURA"
        print(f"\n🔄 Cambiado a {mode}")
        if self.test_mode:
            print("   💡 Los clicks se simularán sin capturar coordenadas")
        else:
            print("   💡 Los clicks capturarán coordenadas normalmente")

    def clear_coordinates(self):
        """Limpiar todas las coordenadas"""
        if len(self.coordinates) > 0:
            self.coordinates.clear()
            print(f"\n🗑️  Todas las coordenadas han sido eliminadas")
        else:
            print(f"\n💭 No hay coordenadas para eliminar")

    def show_help(self):
        """Mostrar ayuda rápida"""
        print("\n" + "📖" * 20)
        print("  AYUDA RÁPIDA")
        print("📖" * 20)
        print("🔘 ESPACIO → Capturar coordenada")
        print("🔘 ENTER  → Finalizar captura")
        print("🔘 ESC    → Cancelar y salir")
        print("🔘 T      → Alternar modo prueba")
        print("🔘 C      → Limpiar coordenadas")
        print("🔘 H      → Mostrar esta ayuda")
        print("📖" * 20)
        
    def capture_auto_mode(self):
        """Modo automático mejorado para sistemas sin msvcrt"""
        print("\n🤖 MODO AUTOMÁTICO ACTIVADO")
        print("⏰ Tienes 5 segundos para posicionar el mouse en cada punto")
        print("🛑 Presiona Ctrl+C para cancelar en cualquier momento")
        print()

        try:
            # Preguntar cuántas coordenadas capturar
            while True:
                try:
                    num_coords = input(f"¿Cuántas coordenadas quieres capturar? (1-{self.max_coords}): ").strip()
                    if not num_coords:
                        num_coords = 7  # Valor por defecto
                    else:
                        num_coords = int(num_coords)

                    if 1 <= num_coords <= self.max_coords:
                        break
                    else:
                        print(f"❌ Número inválido. Debe estar entre 1 y {self.max_coords}")
                except ValueError:
                    print("❌ Por favor ingresa un número válido")
                except KeyboardInterrupt:
                    print("\n❌ Captura cancelada")
                    return False

            print(f"\n🎯 Capturando {num_coords} coordenadas...")
            print("📍 Posiciona el mouse y espera la cuenta regresiva\n")

            for i in range(num_coords):
                print(f"🔄 Preparando captura {i+1}/{num_coords}...")

                # Cuenta regresiva con posición en tiempo real
                for countdown in range(5, 0, -1):
                    x, y = self.get_mouse_position()
                    print(f"\r📍 Posición: ({x:4d}, {y:4d}) - ⏰ Capturando en {countdown}s...", end="", flush=True)
                    time.sleep(1)

                # Capturar coordenada final
                x, y = self.get_mouse_position()
                self.coordinates.append([x, y])
                print(f"\n✅ Coordenada {i+1} capturada: ({x}, {y})")

                # Pausa entre capturas (excepto la última)
                if i < num_coords - 1:
                    print("⏳ Pausa de 2 segundos antes de la siguiente captura...")
                    time.sleep(2)
                    print()

        except KeyboardInterrupt:
            print("\n\n❌ Captura cancelada por el usuario")
            return False
        except Exception as e:
            print(f"\n❌ Error durante la captura: {e}")
            return False

        print(f"\n🎉 ¡Captura automática completada con {len(self.coordinates)} coordenadas!")
        return True
        
    def save_to_config(self):
        """Guardar coordenadas en config.json con manejo robusto"""
        config_file = 'config.json'
        backup_file = f'config_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'

        print(f"\n💾 Guardando configuración...")

        try:
            # Crear backup si existe configuración previa
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        existing_config = json.load(f)

                    # Crear backup
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(existing_config, f, indent=2, ensure_ascii=False)
                    print(f"📋 Backup creado: {backup_file}")

                    config = existing_config
                except json.JSONDecodeError:
                    print(f"⚠️  {config_file} corrupto. Creando configuración nueva...")
                    config = self.create_default_config()
                except Exception as e:
                    print(f"⚠️  Error leyendo {config_file}: {e}. Creando configuración nueva...")
                    config = self.create_default_config()
            else:
                print("📄 Creando nuevo archivo de configuración...")
                config = self.create_default_config()

            # Actualizar coordenadas
            old_coords = len(config.get('coordinates', []))
            config['coordinates'] = self.coordinates

            # Agregar metadatos
            config['metadata'] = {
                'created_by': 'AutoClick GUI Coordinate Helper v2.0',
                'last_updated': datetime.now().isoformat(),
                'total_coordinates': len(self.coordinates),
                'previous_coordinates': old_coords
            }

            # Guardar archivo con formato bonito
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False, sort_keys=True)

            print(f"✅ Configuración guardada exitosamente en {config_file}")
            print(f"📊 Coordenadas: {old_coords} → {len(self.coordinates)}")

            # Validar archivo guardado
            self.validate_saved_config(config_file)

        except Exception as e:
            print(f"❌ Error guardando configuración: {e}")
            print("💡 Intentando guardar en archivo alternativo...")

            try:
                alt_file = f'config_emergency_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
                config = self.create_default_config()
                config['coordinates'] = self.coordinates

                with open(alt_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                print(f"✅ Configuración guardada en archivo alternativo: {alt_file}")
                print("💡 Renombra este archivo a 'config.json' para usarlo")

            except Exception as e2:
                print(f"❌ Error crítico guardando configuración: {e2}")
                return False

        return True

    def validate_saved_config(self, config_file):
        """Validar que el archivo guardado sea correcto"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            coords = config.get('coordinates', [])
            if len(coords) != len(self.coordinates):
                print("⚠️  Advertencia: El número de coordenadas guardadas no coincide")
                return False

            print("✅ Archivo de configuración validado correctamente")
            return True

        except Exception as e:
            print(f"⚠️  Error validando archivo guardado: {e}")
            return False

    def create_default_config(self):
        """Crear configuración por defecto mejorada"""
        return {
            "coordinates": [],
            "timing": {
                "reload_delay_seconds": 20,
                "cycle_hours": 2,
                "min_click_delay_ms": 50,
                "max_click_delay_ms": 150
            },
            "browser": {
                "window_keywords": ["chrome", "firefox", "edge", "opera", "brave", "mozilla"]
            },
            "advanced": {
                "failsafe_enabled": True,
                "log_clicks": True,
                "random_delays": True,
                "browser_detection": True
            }
        }
        
    def show_summary(self):
        """Mostrar resumen detallado de coordenadas capturadas"""
        print("\n" + "🎯" * 25)
        print("  RESUMEN DE COORDENADAS CAPTURADAS")
        print("🎯" * 25)

        if not self.coordinates:
            print("❌ No se capturaron coordenadas")
            return

        print(f"📊 Total de coordenadas: {len(self.coordinates)}")
        print(f"📅 Fecha de captura: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # Mostrar coordenadas en formato tabla
        print("📍 COORDENADAS CAPTURADAS:")
        print("   " + "-" * 35)
        print("   | Click |    X    |    Y    |")
        print("   " + "-" * 35)

        for i, (x, y) in enumerate(self.coordinates, 1):
            print(f"   |   {i:2d}  | {x:6d}  | {y:6d}  |")

        print("   " + "-" * 35)

        # Estadísticas adicionales
        if len(self.coordinates) > 1:
            x_coords = [coord[0] for coord in self.coordinates]
            y_coords = [coord[1] for coord in self.coordinates]

            print(f"\n📈 ESTADÍSTICAS:")
            print(f"   🔸 Rango X: {min(x_coords)} - {max(x_coords)} (ancho: {max(x_coords) - min(x_coords)}px)")
            print(f"   🔸 Rango Y: {min(y_coords)} - {max(y_coords)} (alto: {max(y_coords) - min(y_coords)}px)")
            print(f"   🔸 Centro aproximado: ({sum(x_coords)//len(x_coords)}, {sum(y_coords)//len(y_coords)})")

        print(f"\n✅ Las coordenadas han sido guardadas en config.json")
        print(f"🚀 Ya puedes ejecutar AutoClick GUI con: python autoclick.py")
        print("🎯" * 25)

    def show_menu(self):
        """Mostrar menú de opciones"""
        print("\n" + "🎮" * 20)
        print("  MENÚ DE OPCIONES")
        print("🎮" * 20)
        print("1️⃣  Capturar nuevas coordenadas")
        print("2️⃣  Ver coordenadas existentes")
        print("3️⃣  Probar coordenadas existentes")
        print("4️⃣  Limpiar todas las coordenadas")
        print("5️⃣  Salir")
        print("🎮" * 20)

    def load_existing_coordinates(self):
        """Cargar coordenadas existentes"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)

                coords = config.get('coordinates', [])
                if coords:
                    print(f"\n📋 Coordenadas existentes encontradas: {len(coords)}")
                    for i, (x, y) in enumerate(coords, 1):
                        print(f"   {i}. ({x}, {y})")
                    return coords
                else:
                    print("\n📭 No hay coordenadas guardadas en config.json")
                    return []
            else:
                print("\n📄 No se encontró archivo config.json")
                return []

        except Exception as e:
            print(f"\n❌ Error cargando coordenadas: {e}")
            return []

    def test_existing_coordinates(self):
        """Probar coordenadas existentes"""
        coords = self.load_existing_coordinates()
        if not coords:
            return

        print(f"\n🧪 MODO PRUEBA - Probando {len(coords)} coordenadas")
        print("⏰ Se moverá el cursor a cada posición con pausa de 2 segundos")

        try:
            input("\n🚀 Presiona Enter para comenzar la prueba...")

            for i, (x, y) in enumerate(coords, 1):
                print(f"\n🎯 Probando coordenada {i}/{len(coords)}: ({x}, {y})")
                self.user32.SetCursorPos(x, y)
                print(f"   ✅ Cursor movido a ({x}, {y})")

                if i < len(coords):
                    print("   ⏳ Esperando 2 segundos...")
                    time.sleep(2)

            print(f"\n🎉 Prueba completada para {len(coords)} coordenadas")

        except KeyboardInterrupt:
            print(f"\n❌ Prueba cancelada por el usuario")
        except Exception as e:
            print(f"\n❌ Error durante la prueba: {e}")

def main():
    """Función principal mejorada con menú"""
    print("🖱️" * 30)
    print("  AUTOCLICK GUI - CAPTURADOR DE COORDENADAS v2.0")
    print("🖱️" * 30)

    helper = CoordinateHelper()

    try:
        while True:
            helper.show_menu()

            try:
                choice = input("\n🎯 Selecciona una opción (1-5): ").strip()

                if choice == '1':
                    print("\n🚀 Iniciando captura de coordenadas...")
                    if helper.capture_coordinates():
                        if helper.save_to_config():
                            helper.show_summary()

                elif choice == '2':
                    helper.load_existing_coordinates()

                elif choice == '3':
                    helper.test_existing_coordinates()

                elif choice == '4':
                    coords = helper.load_existing_coordinates()
                    if coords:
                        confirm = input(f"\n⚠️  ¿Estás seguro de eliminar {len(coords)} coordenadas? (s/N): ").strip().lower()
                        if confirm in ['s', 'si', 'sí', 'y', 'yes']:
                            helper.coordinates = []
                            if helper.save_to_config():
                                print("🗑️  Todas las coordenadas han sido eliminadas")
                        else:
                            print("❌ Operación cancelada")

                elif choice == '5':
                    print("\n👋 ¡Hasta luego! Gracias por usar AutoClick GUI")
                    break

                else:
                    print("❌ Opción inválida. Por favor selecciona 1-5")

            except KeyboardInterrupt:
                print("\n\n👋 Saliendo...")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")

            # Pausa antes de mostrar el menú nuevamente
            if choice != '5':
                input("\n⏳ Presiona Enter para continuar...")

    except Exception as e:
        print(f"\n❌ Error crítico: {e}")
        print("💡 Asegúrate de estar ejecutando en Windows con Python 3.7+")

    finally:
        print("\n🖱️ Capturador de coordenadas finalizado")

if __name__ == "__main__":
    main()
