#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Herramienta para obtener coordenadas del mouse
Útil para configurar los puntos de click en config.json
"""

import time
import ctypes
import json
import os

class CoordinateHelper:
    def __init__(self):
        self.user32 = ctypes.windll.user32
        self.coordinates = []
        
    def get_mouse_position(self):
        """Obtener posición actual del mouse"""
        point = ctypes.wintypes.POINT()
        self.user32.GetCursorPos(ctypes.byref(point))
        return point.x, point.y
        
    def capture_coordinates(self):
        """Capturar 7 coordenadas para los clicks"""
        print("=" * 60)
        print("  CAPTURADOR DE COORDENADAS PARA AUTOCLICK")
        print("=" * 60)
        print()
        print("INSTRUCCIONES:")
        print("1. Abre tu navegador web en la página objetivo")
        print("2. Posiciona el mouse sobre cada punto donde quieres hacer click")
        print("3. Presiona ESPACIO para capturar la coordenada")
        print("4. Repite para los 7 puntos de click")
        print()
        print("CONTROLES:")
        print("- ESPACIO: Capturar coordenada actual")
        print("- ESC: Cancelar y salir")
        print("- ENTER: Finalizar cuando tengas las 7 coordenadas")
        print()
        print("=" * 60)
        
        # Importar msvcrt para detectar teclas
        try:
            import msvcrt
        except ImportError:
            print("ERROR: msvcrt no disponible. Usando modo automático...")
            return self.capture_auto_mode()
            
        captured = 0
        
        while captured < 7:
            # Mostrar posición actual del mouse
            x, y = self.get_mouse_position()
            print(f"\rPosición actual: ({x:4d}, {y:4d}) | Capturadas: {captured}/7 | Presiona ESPACIO para capturar", end="", flush=True)
            
            # Verificar si hay tecla presionada
            if msvcrt.kbhit():
                key = msvcrt.getch()
                
                if key == b' ':  # Espacio
                    self.coordinates.append([x, y])
                    captured += 1
                    print(f"\n✓ Coordenada {captured} capturada: ({x}, {y})")
                    
                elif key == b'\x1b':  # ESC
                    print("\n\nCaptura cancelada.")
                    return False
                    
                elif key == b'\r' and captured == 7:  # ENTER
                    break
                    
            time.sleep(0.1)
            
        print(f"\n\n✓ Todas las coordenadas capturadas!")
        return True
        
    def capture_auto_mode(self):
        """Modo automático para sistemas sin msvcrt"""
        print("MODO AUTOMÁTICO:")
        print("Tienes 5 segundos para posicionar el mouse en cada punto")
        print()
        
        for i in range(7):
            print(f"Preparando captura {i+1}/7...")
            for countdown in range(5, 0, -1):
                x, y = self.get_mouse_position()
                print(f"\rPosición: ({x:4d}, {y:4d}) - Capturando en {countdown}s...", end="", flush=True)
                time.sleep(1)
                
            x, y = self.get_mouse_position()
            self.coordinates.append([x, y])
            print(f"\n✓ Coordenada {i+1} capturada: ({x}, {y})")
            print()
            
        return True
        
    def save_to_config(self):
        """Guardar coordenadas en config.json"""
        config_file = 'config.json'
        
        # Cargar configuración existente o crear nueva
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            except json.JSONDecodeError:
                print(f"Advertencia: {config_file} está corrupto o mal formateado. Creando uno nuevo con valores por defecto.")
                config = self.create_default_config()
            except Exception as e:
                print(f"Error inesperado al leer {config_file}: {e}. Creando uno nuevo con valores por defecto.")
                config = self.create_default_config()
        else:
            config = self.create_default_config()
            
        # Actualizar coordenadas
        config['coordinates'] = self.coordinates
        
        # Guardar archivo
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        print(f"✓ Coordenadas guardadas en {config_file}")
        
    def create_default_config(self):
        """Crear configuración por defecto"""
        return {
            "coordinates": [],
            "timing": {
                "reload_delay_seconds": 20,
                "cycle_hours": 2,
                "min_click_delay_ms": 50,
                "max_click_delay_ms": 150
            },
            "browser": {
                "window_keywords": ["chrome", "firefox", "edge", "opera", "brave"]
            }
        }
        
    def show_summary(self):
        """Mostrar resumen de coordenadas capturadas"""
        print("\n" + "=" * 60)
        print("  RESUMEN DE COORDENADAS CAPTURADAS")
        print("=" * 60)
        
        for i, (x, y) in enumerate(self.coordinates, 1):
            print(f"Click {i}: ({x:4d}, {y:4d})")
            
        print("=" * 60)
        print("Las coordenadas han sido guardadas en config.json")
        print("Ya puedes ejecutar AutoClick o AutoClickLite")
        print("=" * 60)

def main():
    """Función principal"""
    helper = CoordinateHelper()
    
    if helper.capture_coordinates():
        helper.save_to_config()
        helper.show_summary()
    
    print("\nPresiona Enter para salir...")
    input()

if __name__ == "__main__":
    main()
