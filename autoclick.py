#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoClick GUI - Programa ultraligero para automatizar clicks en páginas web
Versión: 2.0 con Interfaz Gráfica
Peso: <5MB portable
"""

import time
import random
import json
import logging
import os
import sys
import threading
import re
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import ctypes
from ctypes import wintypes

# Constantes de Windows API
VK_F5 = 0x74
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
SW_RESTORE = 9

class AutoClickerEngine:
    def __init__(self, gui_callback=None):
        self.gui_callback = gui_callback
        self.running = False
        self.paused = False
        self.click_coords = []
        self.reload_delay = 20
        self.cycle_hours = 2
        self.min_delay = 50
        self.max_delay = 150
        self.browser_keywords = ["chrome", "firefox", "edge", "opera", "brave"]

        # Cargar bibliotecas de Windows
        self.user32 = ctypes.windll.user32
        self.kernel32 = ctypes.windll.kernel32

        self.load_config()

    def log(self, message):
        """Enviar mensaje al GUI y al archivo de log"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}"

        # Escribir a archivo
        try:
            with open('autoclicker_log.txt', 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
        except:
            pass

        # Enviar al GUI
        if self.gui_callback:
            self.gui_callback(log_message)

    def load_config(self):
        """Cargar configuración desde archivo JSON"""
        config_file = 'config.json'

        if not os.path.exists(config_file):
            self.create_default_config()

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            self.click_coords = config.get('coordinates', [])
            timing = config.get('timing', {})
            self.reload_delay = timing.get('reload_delay_seconds', 20)
            self.cycle_hours = timing.get('cycle_hours', 2)
            self.min_delay = timing.get('min_click_delay_ms', 50)
            self.max_delay = timing.get('max_click_delay_ms', 150)

            browser = config.get('browser', {})
            self.browser_keywords = browser.get('window_keywords', ["chrome", "firefox", "edge"])

        except Exception as e:
            self.log(f"Error cargando configuración: {e}")
            self.create_default_config()
            self.load_config()

    def create_default_config(self):
        """Crear archivo de configuración por defecto en JSON"""
        default_config = {
            "coordinates": [
                [100, 200], [200, 300], [300, 400], [400, 500],
                [500, 600], [600, 700], [700, 800]
            ],
            "timing": {
                "reload_delay_seconds": 20,
                "cycle_hours": 2,
                "min_click_delay_ms": 50,
                "max_click_delay_ms": 150
            },
            "browser": {
                "window_keywords": ["chrome", "firefox", "edge", "opera", "brave"]
            }
        }

        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)

        self.log("Archivo config.json creado con valores por defecto")

    def save_config(self):
        """Guardar configuración actual"""
        config = {
            "coordinates": self.click_coords,
            "timing": {
                "reload_delay_seconds": self.reload_delay,
                "cycle_hours": self.cycle_hours,
                "min_click_delay_ms": self.min_delay,
                "max_click_delay_ms": self.max_delay
            },
            "browser": {
                "window_keywords": self.browser_keywords
            }
        }

        try:
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            self.log("Configuración guardada")
            return True
        except Exception as e:
            self.log(f"Error guardando configuración: {e}")
            return False

    def find_browser_window(self):
        """Encontrar y activar ventana del navegador usando Windows API"""
        def enum_windows_proc(hwnd, lParam):
            if self.user32.IsWindowVisible(hwnd):
                length = self.user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    self.user32.GetWindowTextW(hwnd, buffer, length + 1)
                    window_title = buffer.value.lower()

                    for keyword in self.browser_keywords:
                        if keyword.lower() in window_title:
                            # Activar ventana
                            self.user32.ShowWindow(hwnd, SW_RESTORE)
                            self.user32.SetForegroundWindow(hwnd)
                            self.log(f"Ventana activada: {buffer.value}")
                            return False  # Detener enumeración
            return True

        try:
            # Definir el tipo de función callback
            EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool,
                                               wintypes.HWND,
                                               ctypes.POINTER(ctypes.c_int))

            # Enumerar ventanas
            self.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)
            time.sleep(0.5)  # Esperar activación
            return True

        except Exception as e:
            self.log(f"Error buscando ventana del navegador: {e}")
            return False

    def click_at(self, x, y):
        """Realizar click usando Windows API directamente"""
        try:
            # Mover cursor
            self.user32.SetCursorPos(x, y)
            time.sleep(0.01)

            # Click down
            self.user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
            time.sleep(0.01)

            # Click up
            self.user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)

            return True
        except Exception as e:
            self.log(f"Error en click ({x}, {y}): {e}")
            return False

    def press_f5(self):
        """Presionar F5 usando Windows API"""
        try:
            # Simular presión de tecla F5
            self.user32.keybd_event(VK_F5, 0, 0, 0)  # Key down
            time.sleep(0.05)
            self.user32.keybd_event(VK_F5, 0, 2, 0)  # Key up
            return True
        except Exception as e:
            self.log(f"Error presionando F5: {e}")
            return False

    def perform_clicks(self):
        """Realizar los clicks con delays aleatorios"""
        self.log("Iniciando secuencia de clicks")

        for i, coord in enumerate(self.click_coords, 1):
            if not self.running:
                break

            # Delay aleatorio entre clicks
            delay = random.randint(self.min_delay, self.max_delay) / 1000.0
            time.sleep(delay)

            # Realizar click
            x, y = coord[0], coord[1]
            if self.click_at(x, y):
                self.log(f"Click {i}/{len(self.click_coords)} en ({x}, {y}) - Delay: {delay:.3f}s")
            else:
                self.log(f"Fallo click {i}/{len(self.click_coords)} en ({x}, {y})")

    def reload_page(self):
        """Recargar página con F5"""
        if self.press_f5():
            self.log("Página recargada (F5)")
        else:
            self.log("Error recargando página")

    def run_cycle(self):
        """Ejecutar un ciclo completo"""
        if not self.running:
            return False

        cycle_start = datetime.now()
        self.log(f"=== INICIANDO CICLO - {cycle_start.strftime('%H:%M:%S')} ===")

        # Buscar y activar navegador
        if not self.find_browser_window():
            self.log("Continuando sin activar ventana específica del navegador")

        # Realizar clicks
        self.perform_clicks()

        if not self.running:
            return False

        # Esperar y recargar
        self.log(f"Esperando {self.reload_delay} segundos...")
        for i in range(self.reload_delay):
            if not self.running:
                return False
            time.sleep(1)

        self.reload_page()

        cycle_end = datetime.now()
        duration = (cycle_end - cycle_start).total_seconds()
        self.log(f"=== CICLO COMPLETADO - {duration:.1f}s ===")

        return True

    def start(self):
        """Iniciar el motor en un hilo separado"""
        if self.running:
            return False

        self.running = True
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()
        return True

    def stop(self):
        """Detener el motor"""
        self.running = False

    def _run_loop(self):
        """Bucle principal del motor"""
        self.log("AutoClicker iniciado")
        self.log(f"Configuración: {len(self.click_coords)} clicks cada {self.cycle_hours}h")

        try:
            while self.running:
                self.run_cycle()

                if not self.running:
                    break

                # Calcular tiempo de espera
                wait_seconds = self.cycle_hours * 3600
                self.log(f"Esperando {self.cycle_hours} horas hasta el próximo ciclo...")

                # Esperar con verificaciones cada segundo
                for i in range(wait_seconds):
                    if not self.running:
                        break
                    time.sleep(1)

        except Exception as e:
            self.log(f"Error crítico: {e}")
        finally:
            self.log("AutoClicker finalizado")

class AutoClickerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AutoClick GUI - Automatizador Web")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Configurar icono y estilo
        try:
            self.root.iconbitmap(default="")
        except:
            pass

        # Variables
        self.engine = None
        self.capturing_coords = False
        self.coord_index = 0

        self.setup_gui()
        self.load_settings()

    def setup_gui(self):
        """Configurar la interfaz gráfica"""
        # Crear notebook para pestañas
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Pestaña de configuración
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuración")
        self.setup_config_tab(config_frame)

        # Pestaña de control
        control_frame = ttk.Frame(notebook)
        notebook.add(control_frame, text="Control")
        self.setup_control_tab(control_frame)

        # Pestaña de logs
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="Logs")
        self.setup_log_tab(log_frame)

    def setup_config_tab(self, parent):
        """Configurar pestaña de configuración"""
        # Frame para coordenadas
        coord_frame = ttk.LabelFrame(parent, text="Coordenadas de Clicks", padding=10)
        coord_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Botones de captura
        btn_frame = ttk.Frame(coord_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 10))

        self.capture_coords_btn = ttk.Button(btn_frame, text="Capturar Coordenadas",
                                            command=self.start_capture)
        self.capture_coords_btn.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="Limpiar Todo",
                  command=self.clear_coords).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="Cargar Config",
                  command=self.load_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="Guardar Config",
                  command=self.save_settings).pack(side=tk.LEFT)

        # Lista de coordenadas
        self.coord_listbox = tk.Listbox(coord_frame, height=8)
        self.coord_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Frame para configuración de tiempos
        timing_frame = ttk.LabelFrame(parent, text="Configuración de Tiempos", padding=10)
        timing_frame.pack(fill=tk.X, padx=5, pady=5)

        # Variables de configuración
        self.reload_delay_var = tk.IntVar(value=20)
        self.cycle_hours_var = tk.IntVar(value=2)
        self.min_delay_var = tk.IntVar(value=50)
        self.max_delay_var = tk.IntVar(value=150)

        # Controles de tiempo
        ttk.Label(timing_frame, text="Delay antes de recargar (seg):").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Spinbox(timing_frame, from_=1, to=300, textvariable=self.reload_delay_var, width=10).grid(row=0, column=1, padx=10, pady=2)

        ttk.Label(timing_frame, text="Intervalo entre ciclos (horas):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Spinbox(timing_frame, from_=1, to=24, textvariable=self.cycle_hours_var, width=10).grid(row=1, column=1, padx=10, pady=2)

        ttk.Label(timing_frame, text="Delay mínimo entre clicks (ms):").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Spinbox(timing_frame, from_=10, to=1000, textvariable=self.min_delay_var, width=10).grid(row=2, column=1, padx=10, pady=2)

        ttk.Label(timing_frame, text="Delay máximo entre clicks (ms):").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Spinbox(timing_frame, from_=10, to=1000, textvariable=self.max_delay_var, width=10).grid(row=3, column=1, padx=10, pady=2)

    def setup_control_tab(self, parent):
        """Configurar pestaña de control"""
        # Frame de estado
        status_frame = ttk.LabelFrame(parent, text="Estado del Sistema", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.status_label = ttk.Label(status_frame, text="Estado: Detenido", font=("Arial", 12, "bold"))
        self.status_label.pack(pady=5)

        # Frame de controles
        control_frame = ttk.LabelFrame(parent, text="Controles", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X)

        self.start_btn = ttk.Button(btn_frame, text="▶ Iniciar", command=self.start_automation, style="Accent.TButton")
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)

        self.stop_btn = ttk.Button(btn_frame, text="⏹ Detener", command=self.stop_automation, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)

        self.test_btn = ttk.Button(btn_frame, text="🧪 Probar Clicks", command=self.test_clicks)
        self.test_btn.pack(side=tk.LEFT, pady=5)

        # Frame de información
        info_frame = ttk.LabelFrame(parent, text="Información", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.info_text = scrolledtext.ScrolledText(info_frame, height=15, state=tk.DISABLED)
        self.info_text.pack(fill=tk.BOTH, expand=True)

    def setup_log_tab(self, parent):
        """Configurar pestaña de logs"""
        # Frame de controles de log
        log_control_frame = ttk.Frame(parent)
        log_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_control_frame, text="Limpiar Logs", command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_control_frame, text="Guardar Logs", command=self.save_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_control_frame, text="Actualizar", command=self.refresh_logs).pack(side=tk.LEFT)

        # Área de logs
        self.log_text = scrolledtext.ScrolledText(parent, height=25, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def start_capture(self):
        """Iniciar captura de coordenadas"""
        if self.capturing_coords:
            return

        self.capture_coords_btn.config(state=tk.DISABLED)
        self.capturing_coords = True
        self.coord_index = 0
        self.coords_temp = []

        # Minimizar ventana
        self.root.iconify()

        # Mostrar instrucciones
        messagebox.showinfo("Captura de Coordenadas",
                           "Posiciona el mouse en cada punto donde quieres hacer click y presiona ESPACIO.\n"
                           "Presiona ESC para cancelar.\n"
                           "La ventana se minimizará para facilitar la captura.")

        # Iniciar captura en hilo separado
        threading.Thread(target=self.capture_coordinates, daemon=True).start()

    def capture_coordinates(self):
        """Capturar coordenadas del mouse"""
        try:
            import msvcrt

            while self.capturing_coords and self.coord_index < 7:  # Máximo 7 coordenadas configurables
                if msvcrt.kbhit():
                    key = msvcrt.getch()

                    if key == b' ':  # Espacio
                        # Obtener posición del mouse
                        point = wintypes.POINT()
                        ctypes.windll.user32.GetCursorPos(ctypes.byref(point))

                        self.coords_temp.append([point.x, point.y])
                        self.coord_index += 1

                        # Actualizar GUI en el hilo principal
                        self.root.after(0, self.update_coord_capture, point.x, point.y)

                    elif key == b'\x1b':  # ESC
                        self.capturing_coords = False
                        self.root.after(0, self.cancel_capture)
                        return

                time.sleep(0.1)

            if self.coord_index >= 7:
                self.capturing_coords = False
                self.root.after(0, self.finish_capture)

        except ImportError:
            # Fallback para sistemas sin msvcrt
            self.root.after(0, self.capture_fallback)

    def update_coord_capture(self, x, y):
        """Actualizar captura de coordenada en GUI"""
        self.add_log(f"Coordenada {self.coord_index} capturada: ({x}, {y})")

    def cancel_capture(self):
        """Cancelar captura"""
        self.capturing_coords = False
        self.capture_coords_btn.config(state=tk.NORMAL)
        self.root.deiconify()
        self.add_log("Captura de coordenadas cancelada")

    def finish_capture(self):
        """Finalizar captura"""
        self.capturing_coords = False
        self.capture_coords_btn.config(state=tk.NORMAL)
        self.root.deiconify()

        # Actualizar lista de coordenadas
        self.coord_listbox.delete(0, tk.END)
        for i, coord in enumerate(self.coords_temp, 1):
            self.coord_listbox.insert(tk.END, f"Click {i}: ({coord[0]}, {coord[1]})")

        self.add_log(f"Captura completada: {len(self.coords_temp)} coordenadas")
        messagebox.showinfo("Captura Completada", f"Se capturaron {len(self.coords_temp)} coordenadas correctamente.")

    def capture_fallback(self):
        """Método alternativo de captura"""
        self.capturing_coords = False
        self.capture_coords_btn.config(state=tk.NORMAL)
        self.root.deiconify()
        messagebox.showwarning("Captura Manual",
                              "La captura automática no está disponible.\n"
                              "Puedes agregar coordenadas manualmente editando config.json")

    def clear_coords(self):
        """Limpiar coordenadas"""
        self.coord_listbox.delete(0, tk.END)
        self.add_log("Coordenadas limpiadas")

    def load_settings(self):
        """Cargar configuración desde archivo"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Cargar coordenadas
                coords = config.get('coordinates', [])
                self.coord_listbox.delete(0, tk.END)
                for i, coord in enumerate(coords, 1):
                    self.coord_listbox.insert(tk.END, f"Click {i}: ({coord[0]}, {coord[1]})")

                # Cargar tiempos
                timing = config.get('timing', {})
                self.reload_delay_var.set(timing.get('reload_delay_seconds', 20))
                self.cycle_hours_var.set(timing.get('cycle_hours', 2))
                self.min_delay_var.set(timing.get('min_click_delay_ms', 50))
                self.max_delay_var.set(timing.get('max_click_delay_ms', 150))

                self.add_log("Configuración cargada desde config.json")
            else:
                self.add_log("No se encontró config.json, usando valores por defecto")
        except Exception as e:
            self.add_log(f"Error cargando configuración: {e}")

    def save_settings(self):
        """Guardar configuración actual"""
        try:
            # Obtener coordenadas de la lista
            coords = []
            for i in range(self.coord_listbox.size()):
                item = self.coord_listbox.get(i)
                match = re.search(r'\((\d+), (\d+)\)', item)
                if match:
                    coords.append([int(match.group(1)), int(match.group(2))])

            # Preservar la configuración del navegador si existe
            browser_config = {"window_keywords": ["chrome", "firefox", "edge", "opera", "brave"]} # Default
            if os.path.exists('config.json'):
                try:
                    with open('config.json', 'r', encoding='utf-8') as f_read:
                        existing_config = json.load(f_read)
                    if "browser" in existing_config and "window_keywords" in existing_config["browser"]:
                        browser_config = existing_config["browser"]
                    else: # Si "browser" o "window_keywords" no está, usa el default pero loguea
                        self.add_log("Advertencia: 'browser' o 'window_keywords' no encontrado en config.json, usando default para browser.")
                except Exception as ex_read: # Ser más específico con la excepción
                    self.add_log(f"Advertencia: No se pudo leer config.json para preservar keywords del navegador: {ex_read}. Usando default.")
            else:
                self.add_log("Info: config.json no existe, usando keywords de navegador por defecto para nueva config.")


            config = {
                "coordinates": coords,
                "timing": {
                    "reload_delay_seconds": self.reload_delay_var.get(),
                    "cycle_hours": self.cycle_hours_var.get(),
                    "min_click_delay_ms": self.min_delay_var.get(),
                    "max_click_delay_ms": self.max_delay_var.get()
                },
                "browser": browser_config
            }

            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.add_log("Configuración guardada en config.json")
            messagebox.showinfo("Guardado", "Configuración guardada correctamente")

        except Exception as e:
            self.add_log(f"Error guardando configuración: {e}")
            messagebox.showerror("Error", f"Error guardando configuración: {e}")

    def start_automation(self):
        """Iniciar automatización"""
        if self.engine and self.engine.running:
            return

        # Validar configuración
        if self.coord_listbox.size() == 0:
            messagebox.showwarning("Sin Coordenadas", "Debes configurar al menos una coordenada de click")
            return

        # Guardar configuración actual
        self.save_settings()

        # Crear y configurar motor
        self.engine = AutoClickerEngine(gui_callback=self.add_log)
        self.engine.load_config()

        # Iniciar motor
        if self.engine.start():
            self.status_label.config(text="Estado: Ejecutándose", foreground="green")
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.add_log("Automatización iniciada")
        else:
            self.add_log("Error iniciando automatización")

    def stop_automation(self):
        """Detener automatización"""
        if self.engine:
            self.engine.stop()

        self.status_label.config(text="Estado: Detenido", foreground="red")
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.add_log("Automatización detenida")

    def test_clicks(self):
        """Probar secuencia de clicks una vez"""
        if self.coord_listbox.size() == 0:
            messagebox.showwarning("Sin Coordenadas", "Debes configurar al menos una coordenada de click")
            return

        # Crear motor temporal para prueba
        test_engine = AutoClickerEngine(gui_callback=self.add_log)
        test_engine.reload_delay = self.reload_delay_var.get()
        test_engine.min_delay = self.min_delay_var.get()
        test_engine.max_delay = self.max_delay_var.get()

        # Obtener coordenadas
        coords = []
        for i in range(self.coord_listbox.size()):
            item = self.coord_listbox.get(i)
            match = re.search(r'\((\d+), (\d+)\)', item)
            if match:
                coords.append([int(match.group(1)), int(match.group(2))])

        test_engine.click_coords = coords
        test_engine.running = True

        self.add_log("Iniciando prueba de clicks...")

        # Ejecutar prueba en hilo separado
        def run_test():
            test_engine.find_browser_window()
            test_engine.perform_clicks()
            self.root.after(0, lambda: self.add_log("Prueba de clicks completada"))

        threading.Thread(target=run_test, daemon=True).start()

    def add_log(self, message):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"

        # Agregar a info_text
        self.info_text.config(state=tk.NORMAL)
        self.info_text.insert(tk.END, log_message)
        self.info_text.see(tk.END)
        self.info_text.config(state=tk.DISABLED)

        # Agregar a log_text
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def clear_logs(self):
        """Limpiar logs"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.config(state=tk.DISABLED)

        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def save_logs(self):
        """Guardar logs a archivo"""
        try:
            logs = self.log_text.get(1.0, tk.END)
            filename = f"autoclick_gui_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(logs)
            messagebox.showinfo("Logs Guardados", f"Logs guardados en {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Error guardando logs: {e}")

    def refresh_logs(self):
        """Actualizar logs desde archivo"""
        try:
            if os.path.exists('autoclicker_log.txt'):
                with open('autoclicker_log.txt', 'r', encoding='utf-8') as f:
                    content = f.read()

                self.log_text.config(state=tk.NORMAL)
                self.log_text.delete(1.0, tk.END)
                self.log_text.insert(1.0, content)
                self.log_text.see(tk.END)
                self.log_text.config(state=tk.DISABLED)
        except Exception as e:
            self.add_log(f"Error actualizando logs: {e}")

    def run(self):
        """Ejecutar la aplicación"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """Manejar cierre de aplicación"""
        if self.engine and self.engine.running:
            self.engine.stop()
        self.root.destroy()

def main():
    """Función principal"""
    try:
        app = AutoClickerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Error Fatal", f"Error iniciando aplicación: {e}")

if __name__ == "__main__":
    main()
