# 🖱️ AutoClick GUI - Automatizador Web con Interfaz Gráfica

Programa portable y sin instalación que automatiza clicks en páginas web con **interfaz gráfica intuitiva** y funciones avanzadas de sigilo.

## 📋 Características Principales

✅ **Interfaz gráfica moderna** con pestañas organizadas
✅ **Captura automática de coordenadas** con un solo click
✅ **Clicks configurables** en coordenadas específicas
✅ **Recarga automática** de página (F5) configurable
✅ **Ciclos automáticos** con intervalos personalizables
✅ **Modo sigiloso** con delays aleatorios anti-detección
✅ **Detección automática** de ventanas del navegador
✅ **Logs en tiempo real** con interfaz visual
✅ **Modo de prueba** para verificar configuración
✅ **Portable** - No requiere instalación
✅ **Ultraligero** - Solo 3-5MB compilado
✅ **Sin dependencias externas** - Solo bibliotecas estándar

## 🎯 Nueva Versión GUI ⭐ RECOMENDADA

### AutoClick GUI (Versión con Interfaz Gráfica)
- **Archivo**: `autoclick.py`
- **Dependencias**: Solo bibliotecas estándar de Python (tkinter, ctypes, json)
- **Tamaño compilado**: ~3-5 MB
- **Configuración**: Interfaz visual + `config.json`
- **Características**: Captura automática, logs visuales, controles intuitivos

## 🛠️ Instalación y Uso

### Opción A: Usar Ejecutable Precompilado (Más Fácil) ⭐

1. **Compilar la versión GUI**:
   ```bash
   build.bat
   ```

2. **Ejecutar la aplicación**:
   ```bash
   dist\AutoClickGUI.exe
   ```

3. **Usar la interfaz gráfica**:
   - Ve a la pestaña "Configuración"
   - Haz click en "Capturar Coordenadas"
   - Posiciona el mouse y presiona ESPACIO en cada punto
   - Ajusta los tiempos según necesites
   - Ve a la pestaña "Control" y presiona "Iniciar"

### Opción B: Ejecutar desde Python

1. **Instalar Python 3.7+** desde [python.org](https://python.org)

2. **Ejecutar directamente** (sin dependencias externas):
   ```bash
   python autoclick.py
   ```

3. **La interfaz gráfica se abrirá automáticamente**

## ⚙️ Configuración

### Configurar Coordenadas de Click

**Método 1: Herramienta Automática** (Recomendado)
```bash
python get_coordinates.py
```
- Sigue las instrucciones en pantalla
- Posiciona el mouse y presiona ESPACIO para capturar
- Las coordenadas se guardan automáticamente

**Método 2: Manual**
Edita `config.json` (versión Lite) o `config.ini` (versión completa):

```json
{
  "coordinates": [
    [100, 200],  // Click 1: X=100, Y=200
    [200, 300],  // Click 2: X=200, Y=300
    [300, 400],  // Click 3: X=300, Y=400
    [400, 500],  // Click 4: X=400, Y=500
    [500, 600],  // Click 5: X=500, Y=600
    [600, 700],  // Click 6: X=600, Y=700
    [700, 800]   // Click 7: X=700, Y=800
  ]
}
```

### Configurar Tiempos

```json
{
  "timing": {
    "reload_delay_seconds": 20,     // Tiempo antes de recargar página
    "cycle_hours": 2,               // Intervalo entre ciclos completos
    "min_click_delay_ms": 50,       // Delay mínimo entre clicks
    "max_click_delay_ms": 150       // Delay máximo entre clicks
  }
}
```

### Configurar Navegadores

```json
{
  "browser": {
    "window_keywords": [
      "chrome", "firefox", "edge", "opera", "brave"
    ]
  }
}
```

## 🔧 Funcionamiento

1. **Detección de Navegador**: Busca y activa ventana del navegador
2. **Secuencia de Clicks**: Realiza 7 clicks con delays aleatorios
3. **Espera**: Aguarda 20 segundos (configurable)
4. **Recarga**: Presiona F5 para recargar la página
5. **Ciclo**: Espera 2 horas y repite todo el proceso

## 📊 Logs y Monitoreo

El programa genera logs detallados en `autoclicker_log.txt`:

```
2024-01-15 10:30:00 - INFO - AutoClicker Lite iniciado
2024-01-15 10:30:01 - INFO - Ventana activada: Google Chrome
2024-01-15 10:30:02 - INFO - Click 1/7 en (100, 200) - Delay: 0.087s
2024-01-15 10:30:03 - INFO - Click 2/7 en (200, 300) - Delay: 0.134s
...
2024-01-15 10:30:25 - INFO - Página recargada (F5)
2024-01-15 10:30:26 - INFO - Esperando 2.0 horas hasta el próximo ciclo...
```

## 🛡️ Modo Sigiloso

- **Delays aleatorios**: Entre 50-150ms entre clicks
- **Variación temporal**: Evita patrones detectables
- **Activación de ventana**: Simula comportamiento humano
- **Logging discreto**: No interfiere con el navegador

## ⚠️ Consideraciones de Seguridad

- **Uso responsable**: Solo en sitios web propios o con autorización
- **Términos de servicio**: Respeta las políticas de los sitios web
- **Detección**: Algunos sitios pueden detectar automatización
- **Failsafe**: Mueve el mouse a la esquina superior izquierda para detener

## 🔧 Solución de Problemas

### El programa no encuentra el navegador
- Verifica que el navegador esté abierto
- Añade el nombre de tu navegador a `window_keywords` en la configuración

### Los clicks no funcionan
- Verifica las coordenadas con `get_coordinates.py`
- Asegúrate de que la página esté visible y activa

### El ejecutable es muy grande
- Usa la versión Lite (`autoclick_lite.py`)
- Considera usar Python portable en lugar de compilar

### Error de permisos
- Ejecuta como administrador si es necesario
- Algunos antivirus pueden bloquear la automatización

## 📁 Estructura de Archivos

```
autoclick/
├── autoclick.py          # Versión completa
├── autoclick_lite.py     # Versión ultraligera ⭐
├── get_coordinates.py    # Herramienta para coordenadas
├── config.json          # Configuración (versión Lite)
├── config.ini           # Configuración (versión completa)
├── requirements.txt      # Dependencias Python
├── build.bat            # Compilador versión completa
├── build_lite.bat       # Compilador versión Lite
├── autoclicker_log.txt  # Logs (se crea automáticamente)
└── README.md            # Esta documentación
```

## 🎯 Casos de Uso

- **Testing web**: Automatizar pruebas repetitivas
- **Formularios**: Completar formularios largos
- **Juegos web**: Automatizar acciones repetitivas
- **Monitoreo**: Mantener sesiones activas
- **Desarrollo**: Probar interfaces de usuario

## 📞 Soporte

Si encuentras problemas:

1. Revisa los logs en `autoclicker_log.txt`
2. Verifica la configuración en `config.json`
3. Prueba con la herramienta `get_coordinates.py`
4. Ejecuta desde Python para ver errores detallados

---

**⚡ Versión recomendada**: AutoClick Lite (`autoclick_lite.py`) - Ultraligero y sin dependencias externas.
