# 🚀 Instrucciones de Instalación - AutoClick GUI

## ⚠️ Requisito: Instalar Python

Antes de usar AutoClick GUI, necesitas tener Python instalado en tu sistema.

### 📥 Descargar e Instalar Python

1. **Ve a la página oficial de Python**: https://python.org/downloads/
2. **Descarga Python 3.8 o superior** (recomendado: última versión estable)
3. **Durante la instalación**:
   - ✅ **IMPORTANTE**: Marca la casilla "Add Python to PATH"
   - ✅ Selecciona "Install for all users" (opcional)
   - ✅ Usa la instalación estándar

### 🔧 Verificar Instalación

Abre una terminal (CMD o PowerShell) y ejecuta:
```bash
python --version
```

Deberías ver algo como: `Python 3.x.x`

## 🎮 Ejecutar AutoClick GUI

### Método 1: Ejecutar Directamente (Recomendado)
```bash
python autoclick.py
```

### Método 2: Compilar a Ejecutable
```bash
build.bat
```
Luego ejecuta: `dist\AutoClickGUI.exe`

## 📱 Uso de la Interfaz Gráfica

### 1. Configurar Coordenadas
- Ve a la pestaña **"Configuración"**
- Haz click en **"Capturar Coordenadas"**
- La ventana se minimizará
- Posiciona el mouse en cada punto donde quieres hacer click
- Presiona **ESPACIO** para capturar cada coordenada
- Presiona **ESC** para cancelar si es necesario

### 2. Ajustar Tiempos
- **Delay antes de recargar**: Tiempo en segundos antes de presionar F5
- **Intervalo entre ciclos**: Horas entre cada ciclo completo
- **Delays de clicks**: Tiempo aleatorio entre clicks (modo sigiloso)

### 3. Controlar la Automatización
- Ve a la pestaña **"Control"**
- **▶ Iniciar**: Comienza la automatización
- **⏹ Detener**: Para la automatización
- **🧪 Probar Clicks**: Ejecuta una secuencia de prueba

### 4. Monitorear Logs
- Ve a la pestaña **"Logs"**
- Observa la actividad en tiempo real
- Guarda logs si es necesario

## 🛡️ Características de Seguridad

- **Modo Sigiloso**: Delays aleatorios entre 50-150ms
- **Detección de Navegador**: Busca automáticamente ventanas del navegador
- **Failsafe**: Mueve el mouse a la esquina superior izquierda para detener
- **Logs Detallados**: Registro completo de todas las acciones

## 🔧 Solución de Problemas

### "Python no se reconoce como comando"
- Reinstala Python marcando "Add Python to PATH"
- O ejecuta desde el directorio de Python

### "No se pueden capturar coordenadas"
- Asegúrate de que la ventana del navegador esté visible
- Usa el método manual editando config.json

### "Los clicks no funcionan"
- Verifica que las coordenadas sean correctas
- Usa el botón "Probar Clicks" para verificar
- Asegúrate de que la página web esté activa

### "El programa se cierra inmediatamente"
- Ejecuta desde terminal para ver errores
- Verifica que no haya antivirus bloqueando

## 📁 Archivos Importantes

- `autoclick.py` - Programa principal con GUI
- `config.json` - Configuración (se crea automáticamente)
- `autoclicker_log.txt` - Logs de actividad
- `build.bat` - Script de compilación

## 🎯 Casos de Uso

- **Testing web**: Automatizar pruebas repetitivas
- **Formularios**: Completar formularios largos
- **Juegos web**: Automatizar acciones repetitivas
- **Monitoreo**: Mantener sesiones activas

## ⚖️ Uso Responsable

- Solo usa en sitios web propios o con autorización
- Respeta los términos de servicio de los sitios web
- No uses para actividades maliciosas o spam
- Algunos sitios pueden detectar automatización

---

**¡Listo para usar!** 🎉

Una vez que tengas Python instalado, simplemente ejecuta:
```bash
python autoclick.py
```

Y disfruta de la interfaz gráfica intuitiva de AutoClick GUI.
