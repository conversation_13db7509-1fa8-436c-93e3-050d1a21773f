@echo off
echo ================================================
echo   AutoClick Lite - Compilador Ultraligero
echo ================================================

echo.
echo [1/3] Verificando Python...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python no encontrado. Instala Python 3.7+ desde python.org
    pause
    exit /b 1
)

echo.
echo [2/3] Instalando PyInstaller (única dependencia)...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo ERROR: No se pudo instalar PyInstaller
    pause
    exit /b 1
)

echo.
echo [3/3] Compilando ejecutable ultraligero...
pyinstaller --onefile --noconsole --name "AutoClickLite" --exclude-module tkinter --exclude-module matplotlib --exclude-module numpy autoclick_lite.py

if %errorlevel% neq 0 (
    echo ERROR: Fallo en la compilación
    pause
    exit /b 1
)

echo.
echo ================================================
echo   COMPILACION EXITOSA!
echo ================================================
echo.
echo Ejecutable: dist\AutoClickLite.exe
echo Tamaño: ~2-3 MB (ultraligero!)
echo.
echo ARCHIVOS NECESARIOS:
echo 1. AutoClickLite.exe (ejecutable principal)
echo 2. config.json (configuración)
echo.
echo El programa creará automáticamente:
echo - autoclicker_log.txt (registro de actividad)
echo.
echo INSTRUCCIONES:
echo 1. Edita config.json con las coordenadas correctas
echo 2. Ejecuta AutoClickLite.exe
echo 3. El programa funcionará en segundo plano
echo.
pause
