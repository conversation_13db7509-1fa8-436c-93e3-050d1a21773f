[COORDINATES]
# Coordenadas para los 7 clicks (X, Y en píxeles)
# Puedes obtener las coordenadas usando herramientas como "Mouse Position" o similar
click_1_x = 100
click_1_y = 200
click_2_x = 200
click_2_y = 300
click_3_x = 300
click_3_y = 400
click_4_x = 400
click_4_y = 500
click_5_x = 500
click_5_y = 600
click_6_x = 600
click_6_y = 700
click_7_x = 700
click_7_y = 800

[TIMING]
# Tiempo en segundos antes de recargar la página
reload_delay_seconds = 20

# Intervalo entre ciclos completos (en horas)
cycle_hours = 2

# Delays aleatorios entre clicks (en milisegundos)
# Para modo sigiloso y evitar detección
min_click_delay_ms = 50
max_click_delay_ms = 150

[BROWSER]
# Títulos de ventanas de navegadores a detectar (separados por comas)
# El programa buscará ventanas que contengan estos nombres
window_titles = Chrome, Firefox, Edge, Opera, Brave, Mozilla
